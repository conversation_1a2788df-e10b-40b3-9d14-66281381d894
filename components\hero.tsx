"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useState } from "react";

export const Hero = () => {
    const [query, setQuery] = useState("");

    const handleSubmit = () => {
        if (query.trim()) { }
    };

    const handleSuggestionClick = (suggestion: string) => {
        setQuery(suggestion);
    };


    return (
        <div className="w-full max-w-8xl mx-auto flex items-center justify-between">
            <div className="flex gap-24 w-full">
                <Card className="w-3/8">
                    <CardHeader>
                        <CardTitle className="text-lg">📖 Ask a Bible Question</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Input
                            placeholder="How to be saved?"
                            className="w-full"
                            value={query}
                            onChange={(e) => setQuery(e.target.value)}
                        />
                        <Button
                            className="w-full bg-orange-800 hover:bg-orange-900"
                            onClick={handleSubmit}
                        >
                            🔗 Generate Graph
                        </Button>

                        <div className="mt-6">
                            <h4 className="font-medium mb-3">💡 Suggested Questions</h4>
                            <div className="space-y-2">
                                {[
                                    "How to be saved?",
                                    "What is faith?",
                                    "Why do we suffer?",
                                    "What is love according to the Bible?",
                                    "How to pray effectively?",
                                    "What does God's plan for my life?",
                                    "How to forgive others?"
                                ].map((question, i) => (
                                    <div
                                        key={i}
                                        className="text-sm text-muted-foreground hover:text-foreground cursor-pointer p-2 rounded hover:bg-accent"
                                        onClick={() => handleSuggestionClick(question)}
                                    >
                                        {question}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card className="w-5/8">
                    <CardHeader>
                        <CardTitle className="text-lg">📊 Knowledge Graph</CardTitle>
                        <p className="text-sm text-muted-foreground">
                            Visualize the relationships between Bible verses and concepts.
                        </p>

                    </CardHeader>
                    <CardContent>
                        <div className="h-[600px] bg-muted rounded-lg flex items-center justify-center mb-4">
                            <p className="text-muted-foreground">Graph will be displayed here</p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};
