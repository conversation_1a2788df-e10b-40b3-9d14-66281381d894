import { AuthButton } from "@/components/auth-button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import Link from "next/link";
import { <PERSON> } from "@/components/hero";

export default function Home() {
  return (
    <main className="min-h-screen flex flex-col items-center">
      <div className="flex-1 w-full flex flex-col items-center">
        <nav className="w-full flex justify-between py-12 px-20">
          <Link href="/" className="text-4xl font-extrabold">RootedGPT</Link>
          <div className="flex gap-4 items-center">
            <ThemeSwitcher />
            <AuthButton />
          </div>
        </nav>
        <div>
          <Hero />
        </div>
        <footer className="w-full flex items-center justify-center py-12 mt-auto">
          <p className="italic text-center text-sm text-muted-foreground font-medium">
            © Copyright Wil<PERSON>, 2025. Almost all rights are reserved.
          </p>
        </footer>
      </div>
    </main>
  );
}

